import { StyleSheet, Dimensions, StatusBar } from 'react-native';

const { width, height } = Dimensions.get('window');
const statusBarHeight = StatusBar.currentHeight || 0;

export const createPublicCardViewStyles = (isDark: boolean) => {
  const backgroundColor = isDark ? '#0A0A0A' : '#F8FAFC';
  const textColor = isDark ? '#FFFFFF' : '#1A202C';
  const secondaryTextColor = isDark ? '#A0AEC0' : '#718096';
  const borderColor = isDark ? '#2D3748' : '#E2E8F0';
  const cardBackgroundColor = isDark ? '#1A202C' : '#FFFFFF'; // Keep for compatibility
  const sectionBackgroundColor = backgroundColor; // Remove card styling
  const gradientStart = isDark ? '#2D3748' : '#FFFFFF';
  const gradientEnd = isDark ? '#1A202C' : '#F7FAFC';

  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor,
    },
    header: {
      padding: 24,
      paddingTop: statusBarHeight + 40,
      paddingBottom: 32,
      alignItems: 'center',
      borderBottomLeftRadius: 24,
      borderBottomRightRadius: 24,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.3 : 0.1,
      shadowRadius: 8,
      elevation: 8,
      position: 'relative',
    },
    // Back button container with solid background
    backButtonContainer: {
      position: 'absolute',
      top: statusBarHeight + 16,
      left: 20,
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.9)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10,
    },
    logoContainer: {
      marginBottom: 16,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.2,
      shadowRadius: 8,
      elevation: 6,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: 40,
      borderWidth: 3,
      borderColor: isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.1)',
    },
    logoPlaceholder: {
      width: 80,
      height: 80,
      borderRadius: 40,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 3,
      borderColor: isDark ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.1)',
      backgroundColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
    },
    logoText: {
      fontSize: 32,
      fontWeight: '800',
      color: '#fff',
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    headerInfo: {
      alignItems: 'center',
      paddingHorizontal: 16,
    },
    businessName: {
      fontSize: 24,
      fontWeight: '800',
      color: '#fff',
      textAlign: 'center',
      marginBottom: 8,
      letterSpacing: 0.5,
      textShadowColor: 'rgba(0, 0, 0, 0.3)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 4,
    },
    ownerName: {
      fontSize: 16,
      color: 'rgba(255,255,255,0.95)',
      textAlign: 'center',
      marginBottom: 6,
      fontWeight: '500',
    },
    title: {
      fontSize: 14,
      color: 'rgba(255,255,255,0.85)',
      textAlign: 'center',
      fontWeight: '400',
      fontStyle: 'italic',
    },
    statsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingVertical: 24,
      paddingHorizontal: 16,
      backgroundColor: isDark ? '#1A202C' : '#FFFFFF',
      marginHorizontal: 20,
      marginTop: -16,
      borderRadius: 16,
      elevation: 6,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: isDark ? 0.3 : 0.15,
      shadowRadius: 8,
      borderWidth: 2,
      borderColor: '#D4AF37',
    },
    statItem: {
      alignItems: 'center',
      flex: 1,
    },
    statNumber: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#D4AF37',
      marginTop: 6,
      marginBottom: 4,
    },
    statLabel: {
      fontSize: 14,
      color: isDark ? '#999' : '#666',
      marginTop: 4,
      fontWeight: '500',
    },
    section: {
      backgroundColor: 'transparent',
      marginHorizontal: 8,
      marginVertical: 16,
      paddingHorizontal: 16,
      paddingVertical: 20,
    },
    sectionTitle: {
      fontSize: 20,
      fontWeight: '700',
      color: textColor,
      marginBottom: 16,
      letterSpacing: 0.3,
    },
    aboutText: {
      fontSize: 14,
      color: textColor,
      lineHeight: 20,
    },
    contactItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 8,
    },
    contactText: {
      fontSize: 14,
      color: textColor,
      marginLeft: 12,
      flex: 1,
    },
    hoursContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    hoursText: {
      fontSize: 14,
      color: textColor,
      marginLeft: 12,
    },
    deliveryText: {
      fontSize: 14,
      color: textColor,
      lineHeight: 20,
    },
    interactionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 16,
      paddingVertical: 16,
      gap: 12,
    },
    interactionButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 12,
      elevation: 3,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.15,
      shadowRadius: 4,
    },
    likeButton: {
      backgroundColor: '#E74C3C',
    },
    subscribeButton: {
      backgroundColor: '#3498DB',
    },
    reviewButton: {
      backgroundColor: '#F39C12',
    },
    interactionButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
      marginLeft: 6,
    },
    activeInteractionButton: {
      opacity: 0.8,
      borderWidth: 2,
      borderColor: 'rgba(255, 255, 255, 0.3)',
    },
    // Stylish Floating Interaction Buttons
    floatingInteractionButtons: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 12,
      paddingBottom: 8,
      gap: 24,
    },
    floatingButtonContainer: {
      alignItems: 'center',
      gap: 8,
    },
    floatingButton: {
      width: 52,
      height: 52,
      borderRadius: 26,
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    likeFloatingButton: {
      backgroundColor: isDark ? '#2C1810' : '#FFF5F5',
      borderWidth: 2,
      borderColor: '#E74C3C',
    },
    subscribeFloatingButton: {
      backgroundColor: isDark ? '#0F1419' : '#F0F8FF',
      borderWidth: 2,
      borderColor: '#3498DB',
    },
    reviewFloatingButton: {
      backgroundColor: isDark ? '#1A1610' : '#FFFBF0',
      borderWidth: 2,
      borderColor: '#F39C12',
    },
    activeFloatingButton: {
      borderWidth: 3,
      transform: [{ scale: 1.05 }],
    },
    buttonLabel: {
      fontSize: 12,
      fontWeight: '600',
      color: textColor,
      textAlign: 'center',
    },
    // Product grid spacing
    productRow: {
      justifyContent: 'space-between',
      paddingHorizontal: 8,
    },
    productItem: {
      flex: 1,
      marginHorizontal: 6,
    },
    productSeparator: {
      height: 12,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      paddingHorizontal: 16,
      paddingVertical: 20,
      gap: 12,
    },
    actionButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: 20,
      paddingVertical: 14,
      borderRadius: 12,
      minWidth: 110,
      justifyContent: 'center',
      elevation: 4,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    actionButtonText: {
      color: '#fff',
      fontSize: 15,
      fontWeight: '700',
      marginLeft: 8,
      letterSpacing: 0.3,
    },
    tabContainer: {
      flexDirection: 'row',
      backgroundColor: 'transparent',
      justifyContent: 'center',
      alignItems: 'center',
      marginHorizontal: 8,
      marginTop: 4,
      marginBottom: 16,
      paddingHorizontal: 8,
    },
    tab: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 8,
      borderRadius: 8,
      marginHorizontal: 2,
    },
    activeTab: {
      backgroundColor: 'transparent',
      borderBottomWidth: 2,
      borderBottomColor: '#D4AF37',
    },
    tabText: {
      fontSize: 13,
      color: isDark ? '#A0A0A0' : '#6B7280',
      marginLeft: 4,
      fontWeight: '500',
    },
    activeTabText: {
      color: '#D4AF37',
      fontWeight: '700',
    },
    loadingContainer: {
      alignItems: 'center',
      paddingVertical: 40,
    },
    loadingText: {
      fontSize: 14,
      color: secondaryTextColor,
      marginTop: 8,
    },
    emptyText: {
      fontSize: 14,
      color: secondaryTextColor,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    productCard: {
      flex: 1,
      backgroundColor: cardBackgroundColor,
      margin: 4,
      borderRadius: 8,
      padding: 8,
      alignItems: 'center',
    },
    productImage: {
      width: 60,
      height: 60,
      borderRadius: 8,
      marginBottom: 8,
    },
    productImagePlaceholder: {
      width: 60,
      height: 60,
      borderRadius: 8,
      backgroundColor: borderColor,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 8,
    },
    productName: {
      fontSize: 12,
      color: textColor,
      textAlign: 'center',
      marginBottom: 4,
    },
    priceContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    price: {
      fontSize: 12,
      fontWeight: 'bold',
      color: '#D4AF37',
    },
    discountedPrice: {
      fontSize: 12,
      fontWeight: 'bold',
      color: '#D4AF37',
      marginRight: 4,
    },
    originalPrice: {
      fontSize: 10,
      color: secondaryTextColor,
      textDecorationLine: 'line-through',
    },
    galleryItem: {
      flex: 1,
      margin: 4,
    },
    galleryImage: {
      width: '100%',
      height: 120,
      borderRadius: 8,
    },
    reviewStatsContainer: {
      marginBottom: 16,
    },
    ratingOverview: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    averageRating: {
      fontSize: 24,
      fontWeight: 'bold',
      color: textColor,
      marginRight: 8,
    },
    starsContainer: {
      flexDirection: 'row',
      marginRight: 8,
    },
    totalReviews: {
      fontSize: 14,
      color: secondaryTextColor,
    },
    reviewCard: {
      backgroundColor: 'transparent',
      padding: 12,
      marginBottom: 12,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
    },
    reviewHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: 8,
    },
    reviewerInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    reviewerAvatar: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: '#D4AF37',
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: 8,
    },
    reviewerInitial: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#fff',
    },
    reviewerName: {
      fontSize: 14,
      fontWeight: '600',
      color: textColor,
      marginBottom: 2,
    },
    reviewStars: {
      flexDirection: 'row',
    },
    reviewDate: {
      fontSize: 12,
      color: secondaryTextColor,
    },
    reviewText: {
      fontSize: 14,
      color: textColor,
      lineHeight: 18,
    },
    footer: {
      alignItems: 'center',
      paddingVertical: 20,
      marginTop: 20,
    },
    footerText: {
      fontSize: 12,
      color: secondaryTextColor,
    },
    // Contact action buttons section styles
    contactButtonsSection: {
      flexDirection: 'row',
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: sectionBackgroundColor,
      marginBottom: 8,
      gap: 12,
    },
    callButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      backgroundColor: '#10B981', // Green for call
      borderRadius: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    callButtonText: {
      fontSize: 14,
      color: '#fff',
      marginLeft: 8,
      fontWeight: '600',
    },
    messageButton: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 12,
      paddingHorizontal: 16,
      backgroundColor: '#25D366', // WhatsApp green
      borderRadius: 8,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    messageButtonText: {
      fontSize: 14,
      color: '#fff',
      marginLeft: 8,
      fontWeight: '600',
    },
    // About tab table styles
    aboutSection: {
      marginBottom: 20,
    },
    aboutTableContainer: {
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
      borderRadius: 12,
      padding: 16,
    },
    aboutTableRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      paddingVertical: 12,
      borderBottomWidth: 1,
      borderBottomColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      minHeight: 40,
    },
    aboutTableLabel: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
      minWidth: 100,
      maxWidth: 120,
    },
    aboutTableLabelText: {
      fontSize: 14,
      color: secondaryTextColor,
      marginLeft: 8,
      fontWeight: '500',
    },
    aboutTableValue: {
      fontSize: 14,
      color: textColor,
      flex: 2,
      textAlign: 'right',
      fontWeight: '400',
      flexWrap: 'wrap',
      lineHeight: 20,
    },
    // Special styles for long addresses
    aboutTableRowColumn: {
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
    addressValueContainer: {
      width: '100%',
      marginTop: 8,
    },
    addressValueText: {
      textAlign: 'left',
      flex: 1,
      width: '100%',
    },
  });
};
